{"version": 3, "file": "BencodeDecoder.js", "sourceRoot": "", "sources": ["../../src/bencode/BencodeDecoder.ts"], "names": [], "mappings": ";;;AAAA,iEAA4D;AAG5D,MAAa,cAAc;IAA3B;QACS,WAAM,GAAG,IAAI,eAAM,EAAE,CAAC;IAkJ/B,CAAC;IAhJQ,IAAI,CAAC,KAAiB;QAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAEM,MAAM,CAAC,KAAiB;QAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAEM,OAAO;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACnB,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC3B,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,IAAI;gBACP,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACxB,KAAK,IAAI;gBACP,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACxB,KAAK,IAAI;gBACP,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACxB,KAAK,IAAI;gBACP,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;YAC1B,KAAK,IAAI;gBACP,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;YACzB,KAAK,GAAG;gBACN,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;YACzB,KAAK,GAAG;gBACN,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;YAC1B;gBACE,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE;oBAAE,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;QACxD,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAEM,QAAQ;QACb,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,IAAI;YAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,SAAS;QACd,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,GAAG;YAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACjE,OAAO,SAAS,CAAC;IACnB,CAAC;IAEM,QAAQ;QACb,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,IAAI;YAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,SAAS;QACd,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,IAAI;YAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAClE,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,QAAQ;QACb,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,QAAQ,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/B,KAAK,IAAI;gBACP,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;YAC1B,KAAK,IAAI;gBACP,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;YACzB;gBACE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAEM,OAAO;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE,EAAE,CAAC;QAC9B,IAAI,SAAS,KAAK,IAAI;YAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAC3D,MAAM,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC;QACxB,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACjB,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAChB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC;YAClB,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YACZ,IAAI,CAAC,GAAG,EAAE;gBAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;YAC/C,CAAC,EAAE,CAAC;QACN,CAAC;QACD,IAAI,CAAC,MAAM;YAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAChD,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;QACb,OAAO,CAAC,MAAM,CAAC;IACjB,CAAC;IAEM,OAAO;QACZ,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC3B,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACvC,CAAC;IAEM,OAAO;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC;QACxB,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACjB,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAChB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC;YAClB,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE;gBAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;YACzD,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YACZ,IAAI,CAAC,GAAG,EAAE;gBAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;YAC/C,CAAC,EAAE,CAAC;QACN,CAAC;QACD,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;QACb,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QACpB,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5B,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,OAAO;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,CAAC,EAAE,EAAE,KAAK,IAAI;YAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAC7D,MAAM,GAAG,GAAc,EAAE,CAAC;QAC1B,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC3B,OAAO,IAAI,EAAE,CAAC;YACZ,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;gBAClB,MAAM,CAAC,CAAC,EAAE,CAAC;gBACX,OAAO,GAAG,CAAC;YACb,CAAC;YACD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAEM,OAAO;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,CAAC,EAAE,EAAE,KAAK,IAAI;YAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAC7D,MAAM,GAAG,GAA4B,EAAE,CAAC;QACxC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC3B,OAAO,IAAI,EAAE,CAAC;YACZ,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;gBAClB,MAAM,CAAC,CAAC,EAAE,CAAC;gBACX,OAAO,GAAG,CAAC;YACb,CAAC;YACD,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAC3B,IAAI,GAAG,KAAK,WAAW;gBAAE,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;YACxD,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;CACF;AAnJD,wCAmJC"}