import { Component, ChangeDetectorRef, AfterViewInit } from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { ApiService } from './service/api.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
})
export class AppComponent implements AfterViewInit {
  title = 'ST2I';

  // on garde l'état en variables
  authenticated = false;
  admin = false;

  constructor(
    private apiService: ApiService,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  ngAfterViewInit(): void {
    // on lit l'état APRÈS l'init de la vue
    this.authenticated = this.apiService.isAuthenticated();
    this.admin = this.apiService.isAdmin();
    this.cdr.detectChanges();   // évite NG0100
  }

  logOut(): void {
    this.apiService.logout();
    this.router.navigate(['/login']);
    this.authenticated = false;
    this.admin = false;
    this.cdr.detectChanges();
  }
}
