# @listr2/prompt-adapter-inquirer

[![Pipeline](https://gitlab.kilic.dev/libraries/listr2/badges/master/pipeline.svg?style=flat-square&ignore_skipped=true)](https://gitlab.kilic.dev/libraries/listr2/-/commits/master) [![Version](https://img.shields.io/npm/v/%40listr2/prompt-adapter-inquirer.svg?style=flat-square&logo=npm)](https://www.npmjs.com/package/%40listr2/prompt-adapter-inquirer?activeTab=versions) [![Downloads](https://img.shields.io/npm/dm/%40listr2/prompt-adapter-inquirer.svg?style=flat-square&logo=npm)](https://www.npmjs.com/package/%40listr2/prompt-adapter-inquirer) [![Size](https://img.shields.io/bundlephobia/min/%40listr2/prompt-adapter-inquirer?style=flat-square&logo=npm)](https://www.npmjs.com/package/%40listr2/prompt-adapter-inquirer) [![Dependencies](https://img.shields.io/librariesio/release/npm/%40listr2/prompt-adapter-inquirer?style=flat-square&logo=npm)](https://www.npmjs.com/package/%40listr2/prompt-adapter-inquirer?activeTab=dependencies)

[![github sponsors](https://img.shields.io/github/sponsors/cenk1cenk2?style=flat-square&logo=github)](https://github.com/sponsors/cenk1cenk2) [![opencollective](https://img.shields.io/opencollective/sponsors/listr2?label=open%20collective&logo=opencollective)](https://opencollective.com/listr2)

**Create beautiful CLI interfaces via easy and logical to-implement task lists that feel alive and interactive.**

---

## Documentation

This is an extension to [`listr2`](https://listr2.kilic.dev/) to create prompts with [`@inquirer/prompts`](https://github.com/SBoudrias/Inquirer.js/blob/master/packages/prompts/README.md).

**[Read the documentation...](https://listr2.kilic.dev/task/prompts.html#inquirer)**
