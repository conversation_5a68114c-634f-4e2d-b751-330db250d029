{"version": 3, "file": "UbjsonEncoder.js", "sourceRoot": "", "sources": ["../../src/ubjson/UbjsonEncoder.ts"], "names": [], "mappings": ";;;AAGA,MAAa,aAAa;IACxB,YAA4B,MAAiC;QAAjC,WAAM,GAAN,MAAM,CAA2B;IAAG,CAAC;IAE1D,MAAM,CAAC,KAAc;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrB,OAAO,MAAM,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAEM,QAAQ,CAAC,KAAc;QAC5B,QAAQ,OAAO,KAAK,EAAE,CAAC;YACrB,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAClC,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,WAAW,CAAC,KAAe,CAAC,CAAC;YAC3C,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC9B,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,IAAI,KAAK,KAAK,IAAI;oBAAE,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;gBACtC,QAAQ,WAAW,EAAE,CAAC;oBACpB,KAAK,KAAK;wBACR,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAkB,CAAC,CAAC;oBAC3C,KAAK,UAAU;wBACb,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAmB,CAAC,CAAC;oBAC5C;wBACE,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAgC,CAAC,CAAC;gBAC3D,CAAC;YACH,CAAC;YACD,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,WAAW,CAAC,KAAe,CAAC,CAAC;YAC3C,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;YAC3B;gBACE,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;IAEM,SAAS;QACd,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAEM,UAAU;QACf,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAEM,YAAY,CAAC,IAAa;QAC/B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAEM,WAAW,CAAC,GAAW;QAC5B,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG;YAAE,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACpD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACvB,CAAC;IAEM,YAAY,CAAC,GAAW;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG;YAAE,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;aACjD,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;YACnC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;QACzC,CAAC;aAAM,IAAI,GAAG,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,GAAG,EAAE,CAAC;YACzC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;YACzB,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;YAChB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC;aAAM,IAAI,GAAG,IAAI,UAAU,IAAI,CAAC,UAAU,IAAI,GAAG,EAAE,CAAC;YACnD,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;YACzB,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;YAChB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC;IACH,CAAC;IAEM,aAAa,CAAC,IAAY;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,IAAI,GAAG,IAAI;YAAE,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;IAC7C,CAAC;IAEM,UAAU,CAAC,KAAa;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACzB,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;QACzB,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACnB,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACvB,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACrC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACnB,CAAC;IAEM,WAAW,CAAC,GAAW;QAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACzB,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;QACzB,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACnB,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACvB,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QACpC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACnB,CAAC;IAEM,QAAQ,CAAC,GAAe;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,CAAC,GAAG,CAAC,UAAa,CAAC,CAAC;QAC1B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC1B,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IAC1B,CAAC;IAEM,QAAQ,CAAC,GAAW;QACzB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,SAAS,GAAG,MAAM,GAAG,CAAC,CAAC;QAC7B,MAAM,QAAQ,GAAG,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;QACnC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAChC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC3B,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;QACzB,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACnB,MAAM,aAAa,GAAG,SAAS,GAAG,IAAI,CAAC;QACvC,IAAI,aAAa,EAAE,CAAC;YAClB,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;YACzB,MAAM,CAAC,CAAC,EAAE,CAAC;QACb,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;YACzB,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC;QACD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,aAAa;YAAE,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;;YAClC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;IAC1C,CAAC;IAEM,aAAa,CAAC,GAAW;QAC9B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACrB,CAAC;IAEM,QAAQ,CAAC,GAAc;QAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAChB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE;YAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;IAEM,QAAQ,CAAC,GAA4B;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACpB,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC;QACD,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;IAEM,QAAQ,CAAC,GAAW;QACzB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,SAAS,GAAG,MAAM,GAAG,CAAC,CAAC;QAC7B,MAAM,QAAQ,GAAG,SAAS,GAAG,CAAC,CAAC;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAChC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC3B,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACnB,MAAM,aAAa,GAAG,SAAS,GAAG,IAAI,CAAC;QACvC,IAAI,aAAa,EAAE,CAAC;YAClB,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;YACzB,MAAM,CAAC,CAAC,EAAE,CAAC;QACb,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;YACzB,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC;QACD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,aAAa;YAAE,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;;YAClC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;IAC1C,CAAC;IAIM,aAAa;QAClB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IAEM,aAAa,CAAC,GAAW;QAC9B,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IAEM,WAAW;QAChB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IAEM,aAAa;QAClB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IAEM,aAAa,CAAC,GAAe;QAClC,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IAEM,WAAW;QAChB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IAEM,aAAa;QAClB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAEM,aAAa,CAAC,IAAa;QAChC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;IAEM,WAAW;QAChB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAEM,aAAa;QAClB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAEM,aAAa,CAAC,GAAW,EAAE,KAAc;QAC9C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IAEM,WAAW;QAChB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;CACF;AAnOD,sCAmOC"}