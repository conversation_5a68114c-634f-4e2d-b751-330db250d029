import { Routes } from '@angular/router';
import { LoginComponent } from './login/login.component';
import { SignupComponent } from './signup/signup.component';
import { UserListComponent } from './user-list/user-list.component';
import { UserFormComponent } from './user-form/user-form.component';
import { TeamListComponent } from './team-list/team-list.component';
import { TeamFormComponent } from './team-form/team-form.component';
import { ProjectListComponent } from './project-list/project-list.component';
import { ProjectFormComponent } from './project-form/project-form.component';
import { ActivityListComponent } from './activity-list/activity-list.component';
import { ActivityFormComponent } from './activity-form/activity-form.component';
import { AbsenceListComponent } from './absence-list/absence-list.component';
import { AbsenceFormComponent } from './absence-form/absence-form.component';
import { HierarchyListComponent } from './hierarchy-list/hierarchy-list.component';
import { HierarchyFormComponent } from './hierarchy-form/hierarchy-form.component';
import { AuthGuard } from './auth.guard';
import { DashboardComponent } from './dashboard/dashboard.component';

export const routes: Routes = [
  { path: '', redirectTo: '/login', pathMatch: 'full' },
  { path: 'login', component: LoginComponent },
  { path: 'signup', component: SignupComponent },

  // Dashboard
  { path: 'dashboard', component: DashboardComponent, canActivate: [AuthGuard] },

  // Users CRUD
  { path: 'users', component: UserListComponent, canActivate: [AuthGuard] },
  { path: 'users/new', component: UserFormComponent, canActivate: [AuthGuard] },
  { path: 'users/edit/:id', component: UserFormComponent, canActivate: [AuthGuard] },

  // Teams CRUD
  { path: 'teams', component: TeamListComponent, canActivate: [AuthGuard] },
  { path: 'teams/new', component: TeamFormComponent, canActivate: [AuthGuard] },
  { path: 'teams/edit/:id', component: TeamFormComponent, canActivate: [AuthGuard] },

  // Projects CRUD
  { path: 'projects', component: ProjectListComponent, canActivate: [AuthGuard] },
  { path: 'projects/new', component: ProjectFormComponent, canActivate: [AuthGuard] },
  { path: 'projects/edit/:id', component: ProjectFormComponent, canActivate: [AuthGuard] },

  // Activities CRUD
  { path: 'activities', component: ActivityListComponent, canActivate: [AuthGuard] },
  { path: 'activities/new', component: ActivityFormComponent, canActivate: [AuthGuard] },
  { path: 'activities/edit/:id', component: ActivityFormComponent, canActivate: [AuthGuard] },

  // Absences CRUD
  { path: 'absences', component: AbsenceListComponent, canActivate: [AuthGuard] },
  { path: 'absences/new', component: AbsenceFormComponent, canActivate: [AuthGuard] },
  { path: 'absences/edit/:id', component: AbsenceFormComponent, canActivate: [AuthGuard] },

  // Hierarchies CRUD
  { path: 'hierarchies', component: HierarchyListComponent, canActivate: [AuthGuard] },
  { path: 'hierarchies/new', component: HierarchyFormComponent, canActivate: [AuthGuard] },
  { path: 'hierarchies/edit/:id', component: HierarchyFormComponent, canActivate: [AuthGuard] },

  // Wildcard route
  { path: '**', redirectTo: '/login' }
];