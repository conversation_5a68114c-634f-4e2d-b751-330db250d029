"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BsonBinary = exports.BsonMaxKey = exports.BsonMinKey = exports.BsonDecimal128 = exports.BsonTimestamp = exports.BsonFloat = exports.BsonInt64 = exports.BsonInt32 = exports.BsonJavascriptCodeWithScope = exports.BsonSymbol = exports.BsonJavascriptCode = exports.BsonDbPointer = exports.BsonObjectId = void 0;
class BsonObjectId {
    constructor(timestamp, process, counter) {
        this.timestamp = timestamp;
        this.process = process;
        this.counter = counter;
    }
}
exports.BsonObjectId = BsonObjectId;
class BsonDbPointer {
    constructor(name, id) {
        this.name = name;
        this.id = id;
    }
}
exports.BsonDbPointer = BsonDbPointer;
class BsonJavascriptCode {
    constructor(code) {
        this.code = code;
    }
}
exports.BsonJavascriptCode = BsonJavascriptCode;
class BsonSymbol {
    constructor(symbol) {
        this.symbol = symbol;
    }
}
exports.BsonSymbol = BsonSymbol;
class BsonJavascriptCodeWithScope {
    constructor(code, scope) {
        this.code = code;
        this.scope = scope;
    }
}
exports.BsonJavascriptCodeWithScope = BsonJavascriptCodeWithScope;
class BsonInt32 {
    constructor(value) {
        this.value = value;
    }
}
exports.BsonInt32 = BsonInt32;
class BsonInt64 {
    constructor(value) {
        this.value = value;
    }
}
exports.BsonInt64 = BsonInt64;
class BsonFloat {
    constructor(value) {
        this.value = value;
    }
}
exports.BsonFloat = BsonFloat;
class BsonTimestamp {
    constructor(increment, timestamp) {
        this.increment = increment;
        this.timestamp = timestamp;
    }
}
exports.BsonTimestamp = BsonTimestamp;
class BsonDecimal128 {
    constructor(data) {
        this.data = data;
    }
}
exports.BsonDecimal128 = BsonDecimal128;
class BsonMinKey {
}
exports.BsonMinKey = BsonMinKey;
class BsonMaxKey {
}
exports.BsonMaxKey = BsonMaxKey;
class BsonBinary {
    constructor(subtype, data) {
        this.subtype = subtype;
        this.data = data;
    }
}
exports.BsonBinary = BsonBinary;
//# sourceMappingURL=values.js.map