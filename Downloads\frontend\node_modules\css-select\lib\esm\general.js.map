{"version": 3, "file": "general.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/css-select/0f0725a9dfeddd2fdb54eda9656cdbab5bbf6be6/src/", "sources": ["general.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAC;AACjD,OAAO,EAAE,qBAAqB,EAAE,MAAM,6BAA6B,CAAC;AAQpE,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAC;AAExC,SAAS,gBAAgB,CACrB,IAAiB,EACjB,OAAmC;IAEnC,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACvC,IAAI,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;QACjC,OAAO,MAAM,CAAC;KACjB;IACD,OAAO,IAAI,CAAC;AAChB,CAAC;AAED;;GAEG;AAEH,MAAM,UAAU,sBAAsB,CAClC,IAAgC,EAChC,QAA0B,EAC1B,OAA2C,EAC3C,OAA2B,EAC3B,YAA6C;IAE7C,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;IAEpC,QAAQ,QAAQ,CAAC,IAAI,EAAE;QACnB,KAAK,YAAY,CAAC,aAAa,CAAC,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;SACtE;QACD,KAAK,YAAY,CAAC,gBAAgB,CAAC,CAAC;YAChC,MAAM,IAAI,KAAK,CACX,wDAAwD,CAC3D,CAAC;SACL;QACD,KAAK,YAAY,CAAC,SAAS,CAAC,CAAC;YACzB,IAAI,QAAQ,CAAC,SAAS,IAAI,IAAI,EAAE;gBAC5B,MAAM,IAAI,KAAK,CACX,2DAA2D,CAC9D,CAAC;aACL;YAED,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,uBAAuB,EAAE;gBACrD,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;aAC/C;YACD,OAAO,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;SACnE;QACD,KAAK,YAAY,CAAC,MAAM,CAAC,CAAC;YACtB,OAAO,qBAAqB,CACxB,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,OAAO,EACP,YAAY,CACf,CAAC;SACL;QACD,OAAO;QACP,KAAK,YAAY,CAAC,GAAG,CAAC,CAAC;YACnB,IAAI,QAAQ,CAAC,SAAS,IAAI,IAAI,EAAE;gBAC5B,MAAM,IAAI,KAAK,CACX,0DAA0D,CAC7D,CAAC;aACL;YAED,IAAI,EAAE,IAAI,EAAE,GAAG,QAAQ,CAAC;YAExB,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE;gBAC3C,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;aAC7B;YAED,OAAO,SAAS,GAAG,CAAC,IAAiB;gBACjC,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,CAAC,CAAC;SACL;QAED,YAAY;QACZ,KAAK,YAAY,CAAC,UAAU,CAAC,CAAC;YAC1B,IACI,OAAO,CAAC,YAAY,KAAK,KAAK;gBAC9B,OAAO,OAAO,KAAK,WAAW,EAChC;gBACE,OAAO,SAAS,UAAU,CAAC,IAAiB;oBACxC,IAAI,OAAO,GAAuB,IAAI,CAAC;oBAEvC,OAAO,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE;wBACnD,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;4BACf,OAAO,IAAI,CAAC;yBACf;qBACJ;oBAED,OAAO,KAAK,CAAC;gBACjB,CAAC,CAAC;aACL;YAED,yDAAyD;YACzD,MAAM,YAAY,GAAG,IAAI,OAAO,EAAe,CAAC;YAChD,OAAO,SAAS,gBAAgB,CAAC,IAAiB;gBAC9C,IAAI,OAAO,GAAuB,IAAI,CAAC;gBAEvC,OAAO,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE;oBACnD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;wBAC5B,IAAI,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;4BACzC,OAAO,IAAI,CAAC;yBACf;wBACD,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;qBAC7B;iBACJ;gBAED,OAAO,KAAK,CAAC;YACjB,CAAC,CAAC;SACL;QACD,KAAK,qBAAqB,CAAC,CAAC;YACxB,4DAA4D;YAC5D,OAAO,SAAS,kBAAkB,CAAC,IAAiB;gBAChD,IAAI,OAAO,GAAuB,IAAI,CAAC;gBAEvC,GAAG;oBACC,IAAI,IAAI,CAAC,OAAO,CAAC;wBAAE,OAAO,IAAI,CAAC;iBAClC,QAAQ,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE;gBAEzD,OAAO,KAAK,CAAC;YACjB,CAAC,CAAC;SACL;QACD,KAAK,YAAY,CAAC,MAAM,CAAC,CAAC;YACtB,OAAO,SAAS,MAAM,CAAC,IAAiB;gBACpC,OAAO,OAAO;qBACT,WAAW,CAAC,IAAI,CAAC;qBACjB,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAC3D,CAAC,CAAC;SACL;QACD,KAAK,YAAY,CAAC,KAAK,CAAC,CAAC;YACrB,OAAO,SAAS,KAAK,CAAC,IAAiB;gBACnC,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACvC,OAAO,MAAM,IAAI,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;YACnE,CAAC,CAAC;SACL;QACD,KAAK,YAAY,CAAC,OAAO,CAAC,CAAC;YACvB,OAAO,SAAS,OAAO,CAAC,IAAiB;gBACrC,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBAE3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACtC,MAAM,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;oBACnC,IAAI,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC;wBAAE,MAAM;oBACxC,IAAI,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,EAAE;wBACvD,OAAO,IAAI,CAAC;qBACf;iBACJ;gBAED,OAAO,KAAK,CAAC;YACjB,CAAC,CAAC;SACL;QACD,KAAK,YAAY,CAAC,QAAQ,CAAC,CAAC;YACxB,IAAI,OAAO,CAAC,kBAAkB,EAAE;gBAC5B,OAAO,SAAS,QAAQ,CAAC,IAAiB;oBACtC,MAAM,QAAQ,GAAG,OAAO,CAAC,kBAAmB,CAAC,IAAI,CAAC,CAAC;oBACnD,OAAO,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC9C,CAAC,CAAC;aACL;YAED,OAAO,SAAS,QAAQ,CAAC,IAAiB;gBACtC,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBAC3C,IAAI,WAAW,CAAC;gBAEhB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACtC,MAAM,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;oBACnC,IAAI,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC;wBAAE,MAAM;oBACxC,IAAI,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;wBAC/B,WAAW,GAAG,cAAc,CAAC;qBAChC;iBACJ;gBAED,OAAO,CAAC,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC;YAC9C,CAAC,CAAC;SACL;QACD,KAAK,YAAY,CAAC,SAAS,CAAC,CAAC;YACzB,IAAI,QAAQ,CAAC,SAAS,IAAI,IAAI,IAAI,QAAQ,CAAC,SAAS,KAAK,GAAG,EAAE;gBAC1D,MAAM,IAAI,KAAK,CACX,oEAAoE,CACvE,CAAC;aACL;YAED,OAAO,IAAI,CAAC;SACf;KACJ;AACL,CAAC"}