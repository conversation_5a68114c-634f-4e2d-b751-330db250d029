{"name": "@ngtools/webpack", "version": "18.2.11", "description": "Webpack plugin that AoT compiles your Angular components and modules.", "main": "./src/index.js", "typings": "src/index.d.ts", "license": "MIT", "keywords": ["Angular CLI", "Angular DevKit", "angular", "aot", "devkit", "plugin", "sdk", "webpack"], "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "author": "Angular Authors", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "homepage": "https://github.com/angular/angular-cli", "peerDependencies": {"@angular/compiler-cli": "^18.0.0", "typescript": ">=5.4 <5.6", "webpack": "^5.54.0"}, "packageManager": "yarn@4.4.0", "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "dependenciesMeta": {"esbuild": {"built": true}, "puppeteer": {"built": true}}}