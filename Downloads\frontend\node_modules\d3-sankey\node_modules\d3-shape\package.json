{"name": "d3-shape", "version": "1.3.7", "description": "Graphical primitives for visualization, such as lines and areas.", "keywords": ["d3", "d3-module", "graphics", "visualization", "canvas", "svg"], "homepage": "https://d3js.org/d3-shape/", "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "<PERSON>", "url": "http://bost.ocks.org/mike"}, "main": "dist/d3-shape.js", "unpkg": "dist/d3-shape.min.js", "jsdelivr": "dist/d3-shape.min.js", "module": "src/index.js", "repository": {"type": "git", "url": "https://github.com/d3/d3-shape.git"}, "files": ["dist/**/*.js", "src/**/*.js"], "scripts": {"pretest": "rollup -c", "test": "tape 'test/**/*-test.js' && eslint src", "prepublishOnly": "rm -rf dist && yarn test", "postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd - && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js"}, "dependencies": {"d3-path": "1"}, "sideEffects": false, "devDependencies": {"d3-polygon": "1", "eslint": "6", "rollup": "1", "rollup-plugin-terser": "5", "tape": "4"}}