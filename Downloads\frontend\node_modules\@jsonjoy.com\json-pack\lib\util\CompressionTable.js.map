{"version": 3, "file": "CompressionTable.js", "sourceRoot": "", "sources": ["../../src/util/CompressionTable.ts"], "names": [], "mappings": ";;;AAAA,4DAAuD;AAEvD,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;AAE3C,MAAa,gBAAgB;IAA7B;QAQY,aAAQ,GAAG,IAAI,GAAG,EAAU,CAAC;QAC7B,gBAAW,GAAG,IAAI,GAAG,EAAW,CAAC;QAEjC,UAAK,GAAc,EAAE,CAAC;QACtB,QAAG,GAAyB,IAAI,GAAG,EAAE,CAAC;IAsJlD,CAAC;IAjKQ,MAAM,CAAC,MAAM,CAAC,KAAc;QACjC,MAAM,KAAK,GAAG,IAAI,gBAAgB,EAAE,CAAC;QACrC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClB,KAAK,CAAC,QAAQ,EAAE,CAAC;QACjB,OAAO,KAAK,CAAC;IACf,CAAC;IAQM,UAAU,CAAC,GAAW;QAC3B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC;IAEM,UAAU,CAAC,KAAgC;QAChD,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,IAAI,CAAC,UAAU,CAAC,KAAe,CAAC,CAAC;YACjC,OAAO;QACT,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAEM,IAAI,CAAC,KAAc;QACxB,QAAQ,OAAO,KAAK,EAAE,CAAC;YACrB,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,IAAI,CAAC,KAAK;oBAAE,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBACzC,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;gBACtC,QAAQ,WAAW,EAAE,CAAC;oBACpB,KAAK,MAAM,CAAC,CAAC,CAAC;wBACZ,MAAM,GAAG,GAAG,KAAgC,CAAC;wBAC7C,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;4BACtB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;4BACrB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;wBACtB,CAAC;wBACD,MAAM;oBACR,CAAC;oBACD,KAAK,KAAK,CAAC,CAAC,CAAC;wBACX,MAAM,GAAG,GAAG,KAAkB,CAAC;wBAC/B,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;wBACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;4BAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;wBAChD,MAAM;oBACR,CAAC;oBACD,KAAK,GAAG,CAAC,CAAC,CAAC;wBACT,MAAM,GAAG,GAAG,KAA8B,CAAC;wBAC3C,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;4BACzB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;4BACf,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnB,CAAC,CAAC,CAAC;wBACH,MAAM;oBACR,CAAC;oBACD,KAAK,GAAG,CAAC,CAAC,CAAC;wBACT,MAAM,GAAG,GAAG,KAAqB,CAAC;wBAClC,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;4BACpB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnB,CAAC,CAAC,CAAC;wBACH,MAAM;oBACR,CAAC;oBACD,KAAK,qCAAiB,CAAC,CAAC,CAAC;wBACvB,MAAM,GAAG,GAAG,KAA0B,CAAC;wBACvC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;wBACzB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBACrB,CAAC;gBACH,CAAC;gBACD,OAAO;YACT,CAAC;YACD;gBACE,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAEM,QAAQ;QACb,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3C,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/B,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACrB,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;YACZ,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC1B,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClB,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAClB,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC7B,MAAM,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACxB,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;gBACvB,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBAChB,IAAI,GAAG,GAAG,CAAC;YACb,CAAC;QACH,CAAC;QACD,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACjD,WAAW,CAAC,IAAI,EAAE,CAAC;QACnB,MAAM,cAAc,GAAG,WAAW,CAAC,MAAM,CAAC;QAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC;YACxC,MAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAC7B,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClB,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;QAC1B,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAEM,QAAQ,CAAC,KAAc;QAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,KAAK,KAAK,SAAS;YAAE,MAAM,IAAI,KAAK,CAAC,UAAU,KAAK,mCAAmC,CAAC,CAAC;QAC7F,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAEM,QAAQ,CAAC,KAAc;QAC5B,QAAQ,OAAO,KAAK,EAAE,CAAC;YACrB,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,IAAI,CAAC,KAAK;oBAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACvC,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;gBACtC,QAAQ,WAAW,EAAE,CAAC;oBACpB,KAAK,MAAM,CAAC,CAAC,CAAC;wBACZ,MAAM,GAAG,GAAG,KAAgC,CAAC;wBAC7C,MAAM,MAAM,GAA4B,EAAE,CAAC;wBAC3C,KAAK,MAAM,GAAG,IAAI,GAAG;4BAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;wBAC5E,OAAO,MAAM,CAAC;oBAChB,CAAC;oBACD,KAAK,KAAK,CAAC,CAAC,CAAC;wBACX,MAAM,GAAG,GAAG,KAAkB,CAAC;wBAC/B,MAAM,MAAM,GAAc,EAAE,CAAC;wBAC7B,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;wBACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;4BAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjE,OAAO,MAAM,CAAC;oBAChB,CAAC;oBACD,KAAK,GAAG,CAAC,CAAC,CAAC;wBACT,MAAM,GAAG,GAAG,KAA8B,CAAC;wBAC3C,MAAM,MAAM,GAAG,IAAI,GAAG,EAAoB,CAAC;wBAC3C,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;4BACzB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;wBACvD,CAAC,CAAC,CAAC;wBACH,OAAO,MAAM,CAAC;oBAChB,CAAC;oBACD,KAAK,GAAG,CAAC,CAAC,CAAC;wBACT,MAAM,GAAG,GAAG,KAAqB,CAAC;wBAClC,MAAM,MAAM,GAAG,IAAI,GAAG,EAAW,CAAC;wBAClC,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;4BACpB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;wBACnC,CAAC,CAAC,CAAC;wBACH,MAAM;oBACR,CAAC;oBACD,KAAK,qCAAiB,CAAC,CAAC,CAAC;wBACvB,MAAM,GAAG,GAAG,KAA0B,CAAC;wBACvC,MAAM,MAAM,GAAG,IAAI,qCAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;wBACrF,OAAO,MAAM,CAAC;oBAChB,CAAC;gBACH,CAAC;gBACD,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACvC,CAAC;YACD,OAAO,CAAC,CAAC,CAAC;gBACR,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAlKD,4CAkKC"}